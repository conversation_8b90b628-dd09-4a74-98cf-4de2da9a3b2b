const crypto = require("crypto")
const axios = require("axios")
const aiConfig = require("../config/ai")
const { STREAM_PROMPT } = require("../config/prompt")

function generateActionId() {
  return crypto.randomBytes(16).toString("hex")
}

function buildAIRequest(prompt) {
  return JSON.stringify({
    messages: [
      {
        role: "system",
        content: STREAM_PROMPT,
      },
      {
        role: "user",
        content: `请为以下主题生成思维导图：${prompt}`,
      },
    ],
    stream: true,
    model: aiConfig.modle_name,
    max_tokens: 100,
    temperature: 0.7,
    stop: ["STOP", "生成结束", "end_of_turn"],
  })
}

/**
 * 解析一行markdown为节点
 * @param {string} line
 * @returns {object|null}
 */
function parseLineToNode(line) {
  const trimmed = line.trim()
  if (!trimmed.startsWith("-")) return null
  const indent = line.search(/\S|$/)
  const level = indent / 2 + 1
  const text = trimmed.slice(1).trim()
  if (!text) return null
  return {
    id: `node_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`,
    text,
    level,
    children: [],
  }
}

/**
 * 发送AI请求，流式返回解析后的节点
 * @param {string} prompt
 * @param {function} onData ({type: 'node'|'done', node?}) 回调
 * @param {function} onError (err) 错误回调
 * @param {function} onEnd () 完成回调
 */
async function sendAIRequest(prompt, onData, onError, onEnd) {
  try {
    const requestData = buildAIRequest(prompt)

    const response = await axios({
      method: "POST",
      url: `https://${aiConfig.AI_HOST_URL}/api/v3/chat/completions`,
      headers: {
        Authorization: `Bearer ${aiConfig.aiToken}`,
        "Content-Type": "application/json",
      },
      data: requestData,
      responseType: "stream",
      timeout: 30000,
    })

    let lineBuffer = ""
    let shouldStop = false

    response.data.on("data", (chunk) => {
      if (shouldStop) return

      const chunkStr = chunk.toString()
      const lines = chunkStr.split("\n")

      for (const line of lines) {
        if (shouldStop) break
        if (!line.trim()) continue
        if (!line.startsWith("data: ")) continue

        const data = line.slice(6).trim()

        if (data === "[DONE]") {
          onData && onData({ type: "done" })
          shouldStop = true
          onEnd && onEnd()
          break
        }

        try {
          const parsed = JSON.parse(data)
          const content = parsed.choices?.[0]?.delta?.content
          if (content) {
            lineBuffer += content

            let newlineIndex
            while ((newlineIndex = lineBuffer.indexOf("\n")) !== -1) {
              const oneLine = lineBuffer.slice(0, newlineIndex)
              lineBuffer = lineBuffer.slice(newlineIndex + 1)

              const node = parseLineToNode(oneLine)
              if (node) {
                onData && onData({ type: "node", node })
              }
            }

            if (
              lineBuffer.includes("STOP") ||
              lineBuffer.includes("生成结束")
            ) {
              onData && onData({ type: "done" })
              shouldStop = true
              onEnd && onEnd()
            }
          }
        } catch (e) {
          console.error("解析错误:", e.message)
        }
      }
    })

    response.data.on("end", () => {
      if (lineBuffer.trim()) {
        const node = parseLineToNode(lineBuffer)
        if (node) {
          onData && onData({ type: "node", node })
        }
      }
      onEnd && onEnd()
    })

    response.data.on("error", (err) => {
      onError && onError(err.message)
    })
  } catch (error) {
    onError && onError(error.message)
  }
}

module.exports = {
  sendAIRequest,
  buildAIRequest,
  generateActionId,
}
