const { sendAIRequest } = require("../services/aiSimpleService") // 你的AI请求服务

async function streamMindmap(ctx) {
  const prompt = ctx.query.prompt || "示例主题"

  ctx.set("Content-Type", "text/event-stream")
  ctx.set("Cache-Control", "no-cache")
  ctx.set("Connection", "keep-alive")
  ctx.status = 200

  const res = ctx.res

  // 发送根节点数据
  res.write(
    `data: ${JSON.stringify({
      type: "node",
      node: {
        id: "root",
        text: prompt,
        level: 0,
        children: [],
      },
    })}\n\n`
  )

  await new Promise((resolve) => {
    sendAIRequest(
      prompt,
      (data) => {
        res.write(`data: ${JSON.stringify(data)}\n\n`)
        if (data.type === "done") {
          res.end()
          resolve()
        }
      },
      (err) => {
        res.write(
          `data: ${JSON.stringify({ type: "error", message: err })}\n\n`
        )
        res.end()
        resolve()
      },
      () => {
        res.end()
        resolve()
      }
    )
  })
}

module.exports = {
  streamMindmap,
}
