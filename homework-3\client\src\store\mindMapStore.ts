import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import type {
  MindMapData,
  MindMapNode,
  SelectedNode,
  MenuType,
  ContextMenuState,
} from '../types/mindmap';

const STORAGE_KEY = 'mindmap-data';

// 创建初始数据的函数
const createInitialData = (): MindMapData => {
  const rootId = uuidv4();
  const rootNode: MindMapNode = {
    id: rootId,
    text: '未命名文件',
    level: 1,
    children: [],
    position: { x: 400, y: 300 },
    style: {
      fontWeight: 'normal',
      fontStyle: 'normal',
      color: '#000000',
      borderWidth: 1,
    },
  };

  return {
    nodes: { [rootId]: rootNode },
    rootId,
  };
};

// 从 localStorage 加载数据
const loadFromStorage = (): MindMapData => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY);
    return saved ? JSON.parse(saved) : createInitialData();
  } catch (error) {
    console.error('Failed to load from localStorage:', error);
    return createInitialData();
  }
};

// 保存到 localStorage
const saveToStorage = (data: MindMapData) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('Failed to save to localStorage:', error);
  }
};

interface MindMapStore {
  // 状态
  mindMapData: MindMapData;
  selectedNode: SelectedNode | null;
  activeMenu: MenuType | null;
  contextMenu: ContextMenuState;
  formatToolbarNodeId: string | null; // 当前显示格式化工具栏的节点ID

  // 基础操作
  setActiveMenu: (menu: MenuType | null) => void;
  setSelectedNode: (node: SelectedNode | null) => void;
  setContextMenu: (contextMenu: ContextMenuState) => void;
  setFormatToolbarNodeId: (nodeId: string | null) => void;

  // 节点操作
  updateNode: (nodeId: string, updates: Partial<MindMapNode>) => void;
  addChildNode: (parentId: string) => void;
  addSiblingNode: (currentNodeId: string) => void;
  deleteNode: (nodeId: string) => void;
  toggleNodeCollapse: (nodeId: string) => void;
  calculateNodeTreeHeight: (nodeId: string, nodes: Record<string, MindMapNode>) => number;
  relayoutLevel2Nodes: (parentId: string) => void;
  updateLevel2NodeAndChildren: (nodeId: string, offsetY: number) => void;
  relayoutLevel3NodesAtPosition: (parentId: string, centerY: number) => void;
  relayoutLevel3Nodes: (parentId: string) => void;

  // 样式操作
  updateNodeStyle: (nodeId: string, styleUpdates: Partial<MindMapNode['style']>) => void;
  toggleBold: (nodeId: string) => void;
  toggleItalic: (nodeId: string) => void;
  updateBorderWidth: (nodeId: string, width: number) => void;
  updateTextColor: (nodeId: string, color: string) => void;

  // 数据操作
  resetData: () => void;
  loadData: (data: MindMapData) => void;
}

export const useMindMapStore = create<MindMapStore>((set, get) => ({
  // 初始状态
  mindMapData: loadFromStorage(),
  selectedNode: null,
  activeMenu: 'start',
  contextMenu: {
    isOpen: false,
    position: { x: 0, y: 0 },
  },
  formatToolbarNodeId: null,

  // 基础操作
  setActiveMenu: (menu) => set({ activeMenu: menu }),

  setSelectedNode: (node) => set({ selectedNode: node }),

  setContextMenu: (contextMenu) => set({ contextMenu }),

  setFormatToolbarNodeId: (nodeId) => set({ formatToolbarNodeId: nodeId }),

  // 更新节点
  updateNode: (nodeId, updates) => {
    set((state) => {
      const newMindMapData = {
        ...state.mindMapData,
        nodes: {
          ...state.mindMapData.nodes,
          [nodeId]: {
            ...state.mindMapData.nodes[nodeId],
            ...updates,
          },
        },
      };
      
      // 保存到 localStorage
      saveToStorage(newMindMapData);
      
      return { mindMapData: newMindMapData };
    });
  },

  // 计算节点及其所有子节点的总高度
  calculateNodeTreeHeight: (nodeId: string, nodes: Record<string, MindMapNode>): number => {
    const node = nodes[nodeId];
    if (!node || node.children.length === 0) {
      return 40; // 单个节点的默认高度
    }

    // 计算所有子节点的总高度
    let totalChildrenHeight = 0;
    for (const childId of node.children) {
      totalChildrenHeight += get().calculateNodeTreeHeight(childId, nodes);
    }

    // 加上子节点之间的间距
    const childSpacing = (node.children.length - 1) * 20; // 子节点间距
    return Math.max(40, totalChildrenHeight + childSpacing);
  },

  // 重新布局 level 2 节点的位置（均匀分布在父节点上下方，保持35px子节点区域间距）
  relayoutLevel2Nodes: (parentId: string) => {
    const state = get();
    const parentNode = state.mindMapData.nodes[parentId];
    if (!parentNode || parentNode.level !== 1) return;

    const level2Children = parentNode.children;
    if (level2Children.length === 0) return;

    // 首先确保所有level2节点的level3子节点都已正确布局
    level2Children.forEach(childId => {
      get().relayoutLevel3Nodes(childId);
    });

    // 计算每个level2节点的子节点高度范围
    const nodeHeights = level2Children.map(nodeId => {
      const node = state.mindMapData.nodes[nodeId];
      if (!node || node.children.length === 0) {
        // 没有子节点的情况，高度为节点本身的高度
        return 40;
      }

      // 有子节点的情况，计算子节点区域的总高度
      const childrenCount = node.children.length;
      const verticalSpacing = 60; // level3节点间距
      return (childrenCount - 1) * verticalSpacing + 40; // 子节点区域高度 + 节点本身高度
    });

    // 计算总的布局高度（包括50px间距）
    const totalSpacing = (level2Children.length - 1) * 35; 
    const totalNodeHeight = nodeHeights.reduce((sum, height) => sum + height, 0);
    const totalLayoutHeight = totalNodeHeight + totalSpacing;

    // 计算起始Y位置（最上方节点的中心位置）
    const startY = parentNode.position.y - (totalLayoutHeight / 2);

    // 重新布局所有level2节点
    let currentY = startY;

    level2Children.forEach((nodeId, index) => {
      const nodeHeight = nodeHeights[index];
      const nodeCenterY = currentY + (nodeHeight / 2);

      // 先重新布局该节点的level3子节点（以nodeCenterY为中心）
      get().relayoutLevel3NodesAtPosition(nodeId, nodeCenterY);

      // 然后将level2节点定位到其子节点的中心
      const node = state.mindMapData.nodes[nodeId];
      if (node) {
        get().updateNode(nodeId, {
          position: {
            ...node.position,
            y: nodeCenterY,
          },
        });
      }

      // 移动到下一个节点的起始位置
      currentY += nodeHeight + 35;
    });
  },

  // 在指定位置重新布局level3节点
  relayoutLevel3NodesAtPosition: (parentId: string, centerY: number) => {
    const state = get();
    const parentNode = state.mindMapData.nodes[parentId];
    if (!parentNode || parentNode.level !== 2) return;

    const level3Children = parentNode.children;
    if (level3Children.length === 0) return;

    const verticalSpacing = 60; // 子节点间距

    // 计算所有子节点应该占据的总高度
    const totalHeight = (level3Children.length - 1) * verticalSpacing;

    // 计算起始Y位置（最上方子节点的位置）
    const startY = centerY - (totalHeight / 2);

    // 重新布局所有子节点
    level3Children.forEach((childId, index) => {
      const child = state.mindMapData.nodes[childId];
      if (child) {
        const newY = startY + index * verticalSpacing;
        get().updateNode(childId, {
          position: {
            ...child.position,
            y: newY,
          },
        });
      }
    });
  },

  // 更新level2节点及其所有子节点的位置
  updateLevel2NodeAndChildren: (nodeId: string, offsetY: number) => {
    const state = get();
    const node = state.mindMapData.nodes[nodeId];
    if (!node) return;

    // 更新level2节点本身的位置
    get().updateNode(nodeId, {
      position: {
        ...node.position,
        y: node.position.y + offsetY,
      },
    });

    // 更新所有level3子节点的位置
    node.children.forEach(childId => {
      const child = state.mindMapData.nodes[childId];
      if (child) {
        get().updateNode(childId, {
          position: {
            ...child.position,
            y: child.position.y + offsetY,
          },
        });
      }
    });
  },

  // 重新布局 level 3 节点的位置（均匀分布在父节点上下方）
  relayoutLevel3Nodes: (parentId: string) => {
    const state = get();
    const parentNode = state.mindMapData.nodes[parentId];
    if (!parentNode || parentNode.level !== 2) return;

    const level3Children = parentNode.children;
    if (level3Children.length === 0) return;

    const verticalSpacing = 60; // 子节点间距

    // 计算所有子节点应该占据的总高度
    const totalHeight = (level3Children.length - 1) * verticalSpacing;

    // 计算起始Y位置（最上方子节点的位置）
    const startY = parentNode.position.y - (totalHeight / 2);

    // 重新布局所有子节点
    level3Children.forEach((childId, index) => {
      const child = state.mindMapData.nodes[childId];
      if (child) {
        const newY = startY + index * verticalSpacing;
        get().updateNode(childId, {
          position: {
            ...child.position,
            y: newY,
          },
        });
      }
    });
  },

  // 添加子节点
  addChildNode: (parentId) => {
    const state = get();
    const parentNode = state.mindMapData.nodes[parentId];
    if (!parentNode || parentNode.level >= 3) return;

    const newNodeId = uuidv4();
    const parentPosition = parentNode.position;
    const childLevel = (parentNode.level + 1) as 2 | 3;

    // 计算新节点位置
    const existingChildren = parentNode.children;
    const horizontalOffset = childLevel === 2 ? 300 : 250;

    let newY = parentPosition.y;

    if (existingChildren.length === 0) {
      // 第一个子节点：与父节点同一水平线（直线连接）
      newY = parentPosition.y;
    } else if (childLevel === 2) {
      // Level 2 节点：临时设置位置，稍后会通过relayoutLevel2Nodes重新布局所有同级节点
      const lastChildId = existingChildren[existingChildren.length - 1];
      const lastChild = state.mindMapData.nodes[lastChildId];

      if (lastChild) {
        // 临时位置：简单地放在最后一个节点下方
        newY = lastChild.position.y + 100;
      }
    } else {
      // Level 3 节点：均匀分布在父节点上下方
      const totalChildren = existingChildren.length + 1; // 包括即将添加的新节点
      const verticalSpacing = 60; // 子节点间距

      // 计算所有子节点应该占据的总高度
      const totalHeight = (totalChildren - 1) * verticalSpacing;

      // 计算起始Y位置（最上方子节点的位置）
      const startY = parentPosition.y - (totalHeight / 2);

      // 新节点的位置是最后一个位置
      newY = startY + existingChildren.length * verticalSpacing;

      // 重新布局所有现有的子节点，使它们均匀分布
      setTimeout(() => {
        get().relayoutLevel3Nodes(parentId);
      }, 0);
    }

    const newNode: MindMapNode = {
      id: newNodeId,
      text: '分支主题',
      level: childLevel,
      parentId: parentId,
      children: [],
      position: {
        x: parentPosition.x + horizontalOffset,
        y: newY,
      },
      style: {
        fontWeight: 'normal',
        fontStyle: 'normal',
        color: '#000000',
        borderWidth: 1,
      },
    };

    set((state) => {
      const newMindMapData = {
        ...state.mindMapData,
        nodes: {
          ...state.mindMapData.nodes,
          [newNodeId]: newNode,
          [parentId]: {
            ...state.mindMapData.nodes[parentId],
            children: [...state.mindMapData.nodes[parentId].children, newNodeId],
          },
        },
      };

      // 保存到 localStorage
      saveToStorage(newMindMapData);

      return {
        mindMapData: newMindMapData,
        selectedNode: { id: newNodeId, level: childLevel },
      };
    });

    // 重新布局相关节点
    if (childLevel === 2) {
      // 如果添加的是 level 2 节点，重新布局所有 level 2 节点
      setTimeout(() => {
        get().relayoutLevel2Nodes(parentId);
      }, 0);
    } else if (childLevel === 3) {
      // 如果添加的是 level 3 节点，需要重新布局相关节点

      // 1. 首先重新布局其父节点的所有 level 3 子节点（已经在上面的 setTimeout 中调用了）

      // 2. 然后重新布局其祖父节点的所有 level 2 子节点（因为子树高度发生了变化）
      const level2Parent = state.mindMapData.nodes[parentId];
      if (level2Parent && level2Parent.parentId) {
        setTimeout(() => {
          // console.log('重新布局所有level2节点，因为添加了level3节点');
          get().relayoutLevel2Nodes(level2Parent.parentId!);
        }, 150); // 增加延迟时间，确保 level 3 重新布局完成
      }
    }
  },

  // 添加同级节点（在当前节点下方）
  addSiblingNode: (currentNodeId) => {
    const state = get();
    const currentNode = state.mindMapData.nodes[currentNodeId];
    if (!currentNode || currentNode.level === 1 || !currentNode.parentId) return;

    const parentId = currentNode.parentId;
    const parentNode = state.mindMapData.nodes[parentId];
    if (!parentNode || parentNode.level >= 3) return;

    const newNodeId = uuidv4();
    const parentPosition = parentNode.position;
    const childLevel = currentNode.level as 2 | 3;

    // 计算新节点位置
    const existingChildren = parentNode.children;
    const currentNodeIndex = existingChildren.indexOf(currentNodeId);
    const horizontalOffset = childLevel === 2 ? 300 : 250;

    let newY = parentPosition.y;

    if (childLevel === 2) {
      // Level 2 节点：临时设置位置，稍后会通过relayoutLevel2Nodes重新布局所有同级节点
      newY = currentNode.position.y + 100; // 临时位置
    } else {
      // Level 3 节点：临时设置位置，稍后会重新布局
      newY = currentNode.position.y + 60; // 临时位置
    }

    const newNode: MindMapNode = {
      id: newNodeId,
      text: '分支主题',
      level: childLevel,
      parentId: parentId,
      children: [],
      position: {
        x: parentPosition.x + horizontalOffset,
        y: newY,
      },
      style: {
        fontWeight: 'normal',
        fontStyle: 'normal',
        color: '#000000',
        borderWidth: 1,
      },
    };

    set((state) => {
      // 在当前节点后面插入新节点
      const newChildren = [...parentNode.children];
      newChildren.splice(currentNodeIndex + 1, 0, newNodeId);

      const newMindMapData = {
        ...state.mindMapData,
        nodes: {
          ...state.mindMapData.nodes,
          [newNodeId]: newNode,
          [parentId]: {
            ...state.mindMapData.nodes[parentId],
            children: newChildren,
          },
        },
      };

      // 保存到 localStorage
      saveToStorage(newMindMapData);

      return {
        mindMapData: newMindMapData,
        selectedNode: { id: newNodeId, level: childLevel },
      };
    });

    // 重新布局相关节点
    if (childLevel === 2) {
      // 如果添加的是 level 2 节点，重新布局所有 level 2 节点
      setTimeout(() => {
        get().relayoutLevel2Nodes(parentId);
      }, 0);
    } else if (childLevel === 3) {
      // 如果添加的是 level 3 节点，重新布局相关节点
      setTimeout(() => {
        get().relayoutLevel3Nodes(parentId);
      }, 0);

      // 然后重新布局其祖父节点的所有 level 2 子节点（因为子树高度发生了变化）
      const level2Parent = state.mindMapData.nodes[parentId];
      if (level2Parent && level2Parent.parentId) {
        setTimeout(() => {
          get().relayoutLevel2Nodes(level2Parent.parentId!);
        }, 150);
      }
    }
  },

  // 切换节点的展开/收缩状态
  toggleNodeCollapse: (nodeId) => {
    const state = get();
    const node = state.mindMapData.nodes[nodeId];
    if (!node) return;

    // 切换collapsed状态
    const newCollapsedState = !node.collapsed;

    set((state) => {
      const newMindMapData = {
        ...state.mindMapData,
        nodes: {
          ...state.mindMapData.nodes,
          [nodeId]: {
            ...state.mindMapData.nodes[nodeId],
            collapsed: newCollapsedState,
          },
        },
      };

      // 保存到 localStorage
      saveToStorage(newMindMapData);

      return { mindMapData: newMindMapData };
    });

    // 如果是level2节点且有父节点，需要重新布局level2节点
    if (node.level === 2 && node.parentId) {
      setTimeout(() => {
        get().relayoutLevel2Nodes(node.parentId!);
      }, 0);
    }
  },

  // 删除节点
  deleteNode: (nodeId) => {
    const state = get();
    const nodeToDelete = state.mindMapData.nodes[nodeId];
    if (!nodeToDelete || nodeToDelete.level === 1) return; // 不能删除根节点

    const deletedNodeLevel = nodeToDelete.level;
    const parentId = nodeToDelete.parentId;

    // 递归删除所有子节点
    const deleteNodeRecursively = (id: string, nodes: Record<string, MindMapNode>) => {
      const node = nodes[id];
      if (!node) return nodes;

      // 删除所有子节点
      let updatedNodes = { ...nodes };
      for (const childId of node.children) {
        updatedNodes = deleteNodeRecursively(childId, updatedNodes);
      }

      // 删除当前节点
      delete updatedNodes[id];
      return updatedNodes;
    };

    set((state) => {
      const newNodes = deleteNodeRecursively(nodeId, state.mindMapData.nodes);

      // 从父节点的 children 中移除
      const finalNodes = nodeToDelete.parentId && newNodes[nodeToDelete.parentId]
        ? {
            ...newNodes,
            [nodeToDelete.parentId]: {
              ...newNodes[nodeToDelete.parentId],
              children: newNodes[nodeToDelete.parentId].children.filter((id) => id !== nodeId),
            },
          }
        : newNodes;

      const newMindMapData = {
        ...state.mindMapData,
        nodes: finalNodes,
      };

      // 保存到 localStorage
      saveToStorage(newMindMapData);

      return {
        mindMapData: newMindMapData,
        selectedNode: state.selectedNode?.id === nodeId ? null : state.selectedNode,
      };
    });

    // 重新布局相关节点
    if (deletedNodeLevel === 2 && parentId) {
      // 如果删除的是 level 2 节点，重新布局所有 level 2 节点（不指定变化节点，使用默认布局）
      setTimeout(() => {
        get().relayoutLevel2Nodes(parentId);
      }, 0);
    } else if (deletedNodeLevel === 3 && parentId) {
      // 如果删除的是 level 3 节点，重新布局其父节点的所有 level 3 子节点
      setTimeout(() => {
        get().relayoutLevel3Nodes(parentId);
      }, 0);

      // 同时需要重新布局其祖父节点的所有 level 2 子节点（因为子树高度发生了变化）
      const level2Parent = state.mindMapData.nodes[parentId];
      if (level2Parent && level2Parent.parentId) {
        setTimeout(() => {
          console.log('重新布局所有level2节点，因为删除了level3节点');
          get().relayoutLevel2Nodes(level2Parent.parentId!);
        }, 150); // 增加延迟时间，确保 level 3 重新布局完成
      }
    }
  },

  // 更新节点样式
  updateNodeStyle: (nodeId, styleUpdates) => {
    const state = get();
    const currentNode = state.mindMapData.nodes[nodeId];
    if (!currentNode) return;

    get().updateNode(nodeId, {
      style: {
        ...currentNode.style,
        ...styleUpdates,
      },
    });
  },

  // 切换加粗
  toggleBold: (nodeId) => {
    const state = get();
    const currentNode = state.mindMapData.nodes[nodeId];
    if (!currentNode) return;

    get().updateNodeStyle(nodeId, {
      fontWeight: currentNode.style.fontWeight === 'bold' ? 'normal' : 'bold',
    });
  },

  // 切换斜体
  toggleItalic: (nodeId) => {
    const state = get();
    const currentNode = state.mindMapData.nodes[nodeId];
    if (!currentNode) return;

    get().updateNodeStyle(nodeId, {
      fontStyle: currentNode.style.fontStyle === 'italic' ? 'normal' : 'italic',
    });
  },

  // 更新边框宽度
  updateBorderWidth: (nodeId, width) => {
    get().updateNodeStyle(nodeId, { borderWidth: width });
  },

  // 更新文本颜色
  updateTextColor: (nodeId, color) => {
    get().updateNodeStyle(nodeId, { color });
  },

  // 重置数据
  resetData: () => {
    const newData = createInitialData();
    saveToStorage(newData);
    set({
      mindMapData: newData,
      selectedNode: null,
    });
  },

  // 加载数据
  loadData: (data) => {
    saveToStorage(data);
    set({ mindMapData: data });
  },
}));

