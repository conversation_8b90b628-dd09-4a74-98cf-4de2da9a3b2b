import { useState, useRef, useEffect } from "react";
import {
  Plus,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { Tooltip, TooltipTrigger, TooltipProvider } from "./ui/tooltip";
import { SketchPicker } from "react-color";
import type { MindMapNode as NodeType } from "../types/mindmap";
import { useMindMapStore } from "../store/mindMapStore";
import "./MindMapNode.css";
import { presetColors } from "../config/colors";

interface MindMapNodeProps {
  node: NodeType;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<NodeType>) => void;
  onAddChild: () => void;
  onContextMenu: (e: React.MouseEvent) => void;
  canAddChild: boolean;
  onToggleCollapse?: () => void;
  onDimensionUpdate?: (
    nodeId: string,
    dimensions: { width: number; height: number }
  ) => void;
  onAdjustChildPosition?: (parentId: string) => void;
}

const MindMapNode = ({
  node,
  isSelected,
  onSelect,
  onUpdate,
  onAddChild,
  onContextMenu,
  canAddChild,
  onToggleCollapse,
  onDimensionUpdate,
  onAdjustChildPosition,
}: MindMapNodeProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(node.text);
  const [colorPickerOpen, setColorPickerOpen] = useState(false);
  const [nodeWidthBeforeEdit, setNodeWidthBeforeEdit] = useState<number | null>(
    null
  );
  const [textContentWidth, setTextContentWidth] = useState<number | null>(null);
  const [textLengthBeforeEdit, setTextLengthBeforeEdit] = useState<
    number | null
  >(null);
  const [textWidthBeforeEdit, setTextWidthBeforeEdit] = useState<number | null>(
    null
  );
  const inputRef = useRef<HTMLInputElement>(null);
  const toolbarRef = useRef<HTMLDivElement>(null);
  const nodeRef = useRef<HTMLDivElement>(null);

  // 使用全局状态管理格式化工具栏和布局函数
  const {
    formatToolbarNodeId,
    setFormatToolbarNodeId,
    relayoutLevel2Nodes,
    relayoutLevel3Nodes,
    updateNode,
    mindMapData,
  } = useMindMapStore();
  const showFormatToolbar = formatToolbarNodeId === node.id;

  // 根据文本长度变化调整子节点位置
  const adjustChildrenByTextChange = (
    parentId: string,
    oldTextWidth: number,
    newTextWidth: number
  ) => {
    const widthDiff = newTextWidth - oldTextWidth;
    if (Math.abs(widthDiff) < 1) return; // 忽略微小变化

    const parentNode = mindMapData.nodes[parentId];
    if (!parentNode || parentNode.children.length === 0) return;

    // 递归调整所有后代节点的X位置
    const adjustDescendants = (nodeId: string, currentWidthDiff: number) => {
      const currentNode = mindMapData.nodes[nodeId];
      if (!currentNode) return;

      currentNode.children.forEach((childId) => {
        const child = mindMapData.nodes[childId];
        if (child) {
          // 根据节点层级决定调整策略
          if (parentNode.level === 1) {
            // Level 1 节点：调整所有 Level 2 和 Level 3 子节点
            if (child.level === 2 || child.level === 3) {
              updateNode(childId, {
                position: {
                  ...child.position,
                  x: child.position.x + currentWidthDiff,
                },
              });
            }
          } else if (parentNode.level === 2) {
            // Level 2 节点：只调整直接的 Level 3 子节点
            if (child.level === 3) {
              updateNode(childId, {
                position: {
                  ...child.position,
                  x: child.position.x + currentWidthDiff,
                },
              });
            }
          }

          // 递归调整子节点的子节点
          adjustDescendants(childId, currentWidthDiff);
        }
      });
    };

    adjustDescendants(parentId, widthDiff);
  };

  // 处理双击显示格式化工具栏并进入编辑模式
  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    // 在进入编辑模式前，记录当前节点的宽度和文本长度
    if (nodeRef.current) {
      const rect = nodeRef.current.getBoundingClientRect();
      setNodeWidthBeforeEdit(rect.width);

      // 计算文本内容的实际宽度
      const textElement = nodeRef.current.querySelector(".node-text");
      if (textElement) {
        const textRect = textElement.getBoundingClientRect();
        setTextContentWidth(textRect.width);
        // 记录编辑前的文本渲染宽度
        setTextWidthBeforeEdit(textRect.width);
      }
    }
    setTextLengthBeforeEdit(node.text.length);

    // 显示当前节点的工具栏
    setFormatToolbarNodeId(node.id);
    // 同时进入编辑模式
    setIsEditing(true);
    setEditText(node.text);

    // 双击时同时触发曲线分布和位置调整
    setTimeout(() => {
      // 1. 首先触发曲线分布重新布局
      if (node.children.length > 0) {
        if (node.level === 1) {
          // Level 1 节点：重新布局所有 level 2 子节点
          relayoutLevel2Nodes(node.id);
        } else if (node.level === 2) {
          // Level 2 节点：重新布局所有 level 3 子节点
          relayoutLevel3Nodes(node.id);
          // 同时重新布局其父节点的所有 level 2 节点（保持整体布局一致）
          if (node.parentId) {
            setTimeout(() => {
              relayoutLevel2Nodes(node.parentId!);
            }, 50);
          }
        }
      }

      // 2. 然后触发尺寸更新和位置调整
      if (nodeRef.current && onDimensionUpdate) {
        const rect = nodeRef.current.getBoundingClientRect();
        onDimensionUpdate(node.id, {
          width: rect.width,
          height: rect.height,
        });

        // 3. 最后调整子节点位置（在曲线布局基础上进行微调）
        if (onAdjustChildPosition && node.children.length > 0) {
          setTimeout(() => {
            onAdjustChildPosition(node.id);
          }, 100); // 延迟执行，确保曲线布局完成
        }
      }
    }, 0);
  };

  // 处理编辑完成
  const handleEditComplete = () => {
    if (editText.length >= 1 && editText.length <= 100) {
      onUpdate({ text: editText });

      // 检查文本是否真的发生了变化
      const textChanged = editText !== node.text;

      // 如果文本发生变化，需要重新布局
      if (textChanged && nodeRef.current) {
        setTimeout(() => {
          // 1. 首先触发曲线分布重新布局（如果有子节点）
          if (node.children.length > 0) {
            if (node.level === 1) {
              relayoutLevel2Nodes(node.id);
            } else if (node.level === 2) {
              relayoutLevel3Nodes(node.id);
              // 同时重新布局其父节点的所有 level 2 节点
              if (node.parentId) {
                setTimeout(() => {
                  relayoutLevel2Nodes(node.parentId!);
                }, 50);
              }
            }
          }

          // 2. 然后更新节点尺寸
          if (nodeRef.current && onDimensionUpdate) {
            const rect = nodeRef.current.getBoundingClientRect();
            onDimensionUpdate(node.id, {
              width: rect.width,
              height: rect.height,
            });

            // 3. 最后调整子节点位置（在曲线布局基础上进行微调）
            if (node.children.length > 0 && onAdjustChildPosition) {
              setTimeout(() => {
                onAdjustChildPosition(node.id);
              }, 100);
            }
          }

          // 4. 在所有布局完成后，根据文本宽度变化进行额外的位置调整
          setTimeout(() => {
            // 计算新的文本宽度
            const textElement = nodeRef.current?.querySelector(".node-text");
            let newTextWidth = 0;
            if (textElement) {
              const textRect = textElement.getBoundingClientRect();
              newTextWidth = textRect.width;
            }

            // 如果有记录的编辑前文本宽度，且当前节点有子节点，则根据文本宽度变化调整子节点位置
            if (
              textWidthBeforeEdit !== null &&
              node.children.length > 0 &&
              (node.level === 1 || node.level === 2)
            ) {
              adjustChildrenByTextChange(
                node.id,
                textWidthBeforeEdit,
                newTextWidth
              );
            }
          }, 200); // 延迟执行，确保所有布局完成
        }, 0);
      }
    } else {
      setEditText(node.text); // 恢复原文本
    }
    setIsEditing(false);
    setNodeWidthBeforeEdit(null); // 重置宽度记录
    setTextContentWidth(null); // 重置文本内容宽度记录
    setTextLengthBeforeEdit(null); // 重置文本长度记录
    setTextWidthBeforeEdit(null); // 重置文本宽度记录
    // 编辑完成后保持工具栏显示
    setFormatToolbarNodeId(node.id);
  };

  // 处理编辑取消
  const handleEditCancel = () => {
    setEditText(node.text);
    setIsEditing(false);
    setNodeWidthBeforeEdit(null); // 重置宽度记录
    setTextContentWidth(null); // 重置文本内容宽度记录
    setTextLengthBeforeEdit(null); // 重置文本长度记录
    setTextWidthBeforeEdit(null); // 重置文本宽度记录
    // 编辑取消后保持工具栏显示
    setFormatToolbarNodeId(node.id);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleEditComplete();
    } else if (e.key === "Escape") {
      handleEditCancel();
    }
  };

  // 处理格式化操作
  const handleFormatChange = (
    property: keyof NodeType["style"],
    value: string | number
  ) => {
    onUpdate({
      style: {
        ...node.style,
        [property]: value,
      },
    });
  };

  // 切换加粗
  const toggleBold = () => {
    handleFormatChange(
      "fontWeight",
      node.style.fontWeight === "bold" ? "normal" : "bold"
    );
  };

  // 切换斜体
  const toggleItalic = () => {
    handleFormatChange(
      "fontStyle",
      node.style.fontStyle === "italic" ? "normal" : "italic"
    );
  };

  // 改变颜色
  const changeColor = (color: string) => {
    handleFormatChange("color", color);
  };

  // 自动聚焦输入框
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // 监测节点尺寸变化（仅在编辑模式下）
  useEffect(() => {
    if (nodeRef.current && onDimensionUpdate && isEditing) {
      const updateDimensions = () => {
        if (nodeRef.current) {
          const rect = nodeRef.current.getBoundingClientRect();
          onDimensionUpdate(node.id, {
            width: rect.width,
            height: rect.height,
          });
        }
      };

      // 初始测量
      updateDimensions();

      // 创建 ResizeObserver 来监听尺寸变化
      const resizeObserver = new ResizeObserver(updateDimensions);
      resizeObserver.observe(nodeRef.current);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [node.id, node.text, node.style, onDimensionUpdate, isEditing]);

  // 点击外部关闭工具栏
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        showFormatToolbar &&
        toolbarRef.current &&
        event.target instanceof Node
      ) {
        if (!toolbarRef.current.contains(event.target)) {
          setFormatToolbarNodeId(null);
        }
      }
    };

    if (showFormatToolbar) {
      document.addEventListener("click", handleClickOutside);
      return () => document.removeEventListener("click", handleClickOutside);
    }
  }, [showFormatToolbar, setFormatToolbarNodeId]);

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditing) {
      // 如果点击的不是当前工具栏显示的节点，则收起工具栏
      if (formatToolbarNodeId !== node.id) {
        setFormatToolbarNodeId(null);
      }
      onSelect();
    }
  };

  // 获取节点样式
  const getNodeStyle = () => {
    const baseStyle = {
      fontWeight: node.style.fontWeight,
      fontStyle: node.style.fontStyle,
      color: node.style.color,
      borderWidth: `${node.style.borderWidth}px`, // 使用 !important 确保优先级
      fontSize: "14px", // 默认字体大小
    };

    // 根据层级设置基础样式
    switch (node.level) {
      case 1:
        return {
          ...baseStyle,
          backgroundColor: "#e8f5e8",
          borderColor: "#388e3c",
          padding: "12px 16px",
          fontSize: "16px",
          fontWeight: node.style.fontWeight === "normal" ? "400" : "bold",
        };
      case 2:
        return {
          ...baseStyle,
          backgroundColor: "#f3e5f5",
          borderColor: "#7b1fa2",
          padding: "10px 14px",
          fontSize: "14px",
        };
      case 3:
        return {
          ...baseStyle,
          backgroundColor: "#f6edc1",
          borderColor: "#a7932c",
          padding: "8px 12px",
          fontSize: "12px",
          minWidth: "80px",
          whiteSpace: "nowrap",
        };
      default:
        return { ...baseStyle, fontSize: "14px" };
    }
  };

  const nodeStyle = getNodeStyle();

  const getNodeClasses = () => {
    let classes = "mindmap-node";

    if (isSelected) classes += " selected";
    if (isEditing) classes += " editing";

    switch (node.level) {
      case 1:
        classes += " level-1";
        break;
      case 2:
        classes += " level-2";
        break;
      case 3:
        classes += " level-3";
        break;
    }

    return classes;
  };

  return (
    <div
      className="mindmap-node-container"
      style={{
        left: node.position.x,
        top: node.position.y,
      }}
    >
      {/* 收缩/展开按钮 - 只对level2节点且有子节点时显示 */}
      {node.level === 2 && node.children.length > 0 && (
        <Button
          size="sm"
          variant="ghost"
          className="btn btn-outline btn-sm add-child-button"
          style={{
            left: -27,
          }}
          onClick={(e) => {
            e.stopPropagation();
            onToggleCollapse?.();
          }}
        >
          {node.collapsed ? (
            <ChevronRight className="w-3 h-3" />
          ) : (
            <ChevronDown className="w-3 h-3" />
          )}
        </Button>
      )}

      {/* 节点主体 */}
      <div
        ref={nodeRef}
        className={getNodeClasses()}
        style={nodeStyle}
        onClick={handleClick}
        onDoubleClick={handleDoubleClick}
        onContextMenu={onContextMenu}
      >
        {isEditing ? (
          <input
            ref={inputRef}
            type="text"
            value={editText}
            onChange={(e) => setEditText(e.target.value)}
            onBlur={handleEditComplete}
            onKeyDown={handleKeyDown}
            maxLength={100}
            className="node-input"
            placeholder="输入节点内容"
            aria-label="编辑节点内容"
            style={{
              fontWeight: nodeStyle.fontWeight,
              fontStyle: nodeStyle.fontStyle,
              color: nodeStyle.color,
              fontSize: nodeStyle.fontSize,
              width: `${textContentWidth}px`,
            }}
          />
        ) : (
          <span className="node-text">{node.text}</span>
        )}

        {/* 添加子节点按钮 - 只在节点没有子节点时显示 */}
        {isSelected &&
          canAddChild &&
          !isEditing &&
          node.children.length === 0 && (
            <Button
              size="sm"
              variant="outline"
              className="add-child-button"
              onClick={(e) => {
                e.stopPropagation();
                onAddChild();
              }}
            >
              <Plus className="add-icon" />
            </Button>
          )}
      </div>

      {/* 字符计数提示（编辑时显示） */}
      {isEditing && (
        <div className="char-count">
          {editText.length}/100
          {editText.length === 0 && <span className="error">至少1个字符</span>}
          {editText.length > 100 && <span className="error">超出最大长度</span>}
        </div>
      )}

      {/* 格式化工具栏（双击时显示） */}
      {showFormatToolbar && (
        <div className="format-toolbar" ref={toolbarRef}>
          <TooltipProvider delayDuration={0}>
            <div className="toolbar-content">
              {/* 编辑/完成按钮 */}
              <Button
                size="sm"
                variant="ghost"
                className="toolbar-button text-button"
                disabled
                title="编辑文本"
              >
                T
              </Button>

              {/* 加粗按钮 */}
              <Button
                size="sm"
                variant={node.style.fontWeight === "bold" ? "default" : "ghost"}
                className={`toolbar-button ${node.style.fontWeight === "bold" ? "button-active" : ""}`}
                onClick={toggleBold}
              >
                <Bold className="toolbar-icon" />
              </Button>

              {/* 斜体按钮 */}
              <Button
                size="sm"
                variant={
                  node.style.fontStyle === "italic" ? "default" : "ghost"
                }
                className={`toolbar-button ${node.style.fontStyle === "italic" ? "button-active" : ""}`}
                onClick={toggleItalic}
              >
                <Italic className="toolbar-icon" />
              </Button>

              {/* 下划线按钮 */}
              <Button
                size="sm"
                variant="ghost"
                className="toolbar-button"
                disabled
              >
                <Underline className="toolbar-icon" />
              </Button>

              {/* 删除线按钮 */}
              <Button
                size="sm"
                variant="ghost"
                className="toolbar-button"
                disabled
              >
                <Strikethrough className="toolbar-icon" />
              </Button>

              {/* 字体颜色 */}
              <Popover open={colorPickerOpen} onOpenChange={setColorPickerOpen}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`toolbar-button ${colorPickerOpen ? "button-active" : ""}`}
                      >
                        <svg
                          className="toolbar-icon"
                          viewBox="0 0 1024 1024"
                          version="1.1"
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                        >
                          <path
                            d="M825.6 652.8L544 83.2C537.6 70.4 524.8 64 512 64s-25.6 6.4-32 19.2l-281.6 569.6c-6.4 19.2 0 38.4 19.2 51.2 19.2 6.4 38.4 0 51.2-19.2L384 454.4h275.2l115.2 230.4c6.4 19.2 32 25.6 51.2 19.2 6.4-12.8 12.8-32 0-51.2zM409.6 384L512 172.8 614.4 384H409.6z"
                            fill="#2c2c2c"
                          />
                          <path
                            d="M876.8 960H147.2c-44.8 0-83.2-38.4-83.2-83.2v-19.2c0-51.2 38.4-89.6 83.2-89.6h723.2c44.8 0 83.2 38.4 83.2 83.2v19.2c6.4 51.2-32 89.6-76.8 89.6z"
                            fill={node.style.color || "#000000"}
                          />
                        </svg>
                      </Button>
                    </PopoverTrigger>
                  </TooltipTrigger>
                </Tooltip>
                <PopoverContent className="popover-content-color">
                  <SketchPicker
                    color={node.style.color || "#000000"}
                    onChange={(color) => {
                      changeColor(color.hex);
                    }}
                    onChangeComplete={() => {
                      setColorPickerOpen(false);
                    }}
                    disableAlpha={true}
                    presetColors={presetColors}
                  />
                </PopoverContent>
              </Popover>
              <Button
                size="sm"
                variant="ghost"
                className="toolbar-button"
                disabled
              >
                {/* VIP标识 */}
                <svg
                  className="toolbar-icon"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                >
                  <path
                    d="M512 928H128a32 32 0 0 1-26.88-49.92L345.6 512 101.12 145.92A32 32 0 0 1 128 96h384a32 32 0 0 1 0 64H187.52l223.36 334.08a33.28 33.28 0 0 1 0 35.84L187.52 864H512a32 32 0 0 1 0 64zM640 928a36.48 36.48 0 0 1-17.92-5.12 32.64 32.64 0 0 1-8.96-44.8l256-384a32 32 0 0 1 53.76 35.84l-256 384a33.28 33.28 0 0 1-26.88 14.08z"
                    fill="#4D4D4D"
                  ></path>
                  <path
                    d="M896 928a33.28 33.28 0 0 1-26.88-14.08l-256-384a32 32 0 1 1 53.76-35.84l256 384a32.64 32.64 0 0 1-8.96 44.8 36.48 36.48 0 0 1-17.92 5.12z"
                    fill="#4D4D4D"
                  ></path>
                </svg>
              </Button>
              {/* 列表按钮 */}
              <Button
                size="sm"
                variant="ghost"
                className="toolbar-button"
                disabled
              >
                <List className="toolbar-icon" />
              </Button>

              {/* 有序列表按钮 */}
              <Button
                size="sm"
                variant="ghost"
                className="toolbar-button"
                disabled
              >
                <ListOrdered className="toolbar-icon" />
              </Button>
            </div>
          </TooltipProvider>
        </div>
      )}
    </div>
  );
};

export default MindMapNode;
